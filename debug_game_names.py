#!/usr/bin/env python3
"""
调试游戏名提取功能
"""

import pandas as pd
import re

def extract_game_names(filename):
    """从文件名提取中文名和英文名"""
    if pd.isna(filename) or not isinstance(filename, str):
        return "", ""

    # 清理文件名
    cleaned = filename.strip()
    print(f"原始文件名: {cleaned}")

    # 查找所有方括号内容
    brackets = re.findall(r'\[([^\]]+)\]', cleaned)
    print(f"找到的括号内容: {brackets}")

    if len(brackets) < 3:  # 至少需要3个括号
        print("括号数量不足3个，跳过")
        return "", ""

    chinese_name = ""
    english_name = ""
    potential_names = []  # 存储所有可能的游戏名

    # 遍历所有括号内容，收集可能的游戏名
    for i, content in enumerate(brackets):
        content = content.strip()
        print(f"  处理括号 {i+1}: '{content}'")

        # 跳过空内容和title ID（16位十六进制）
        if not content or re.match(r'^[0-9A-Fa-f]{16}$', content):
            print(f"    跳过: 空内容或title ID")
            continue

        # 跳过日期格式（8位数字）
        if re.match(r'^[0-9]{8}$', content):
            print(f"    跳过: 日期格式")
            continue

        # 跳过"中文"、"英文"等标识词
        if content in ['中文', '英文', 'Chinese', 'English']:
            print(f"    跳过: 标识词")
            continue

        # 收集所有可能的游戏名
        potential_names.append(content)
        print(f"    添加到候选名称: '{content}'")

    print(f"候选游戏名: {potential_names}")

    # 从收集的名称中提取中文名和英文名
    for content in potential_names:
        # 判断是否为纯中文游戏名（主要是中文字符，可能包含少量标点）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        total_chars = len(content.replace(' ', '').replace('：', '').replace(':', ''))
        chinese_ratio = chinese_chars / max(total_chars, 1)
        
        print(f"  分析 '{content}': 中文字符={chinese_chars}, 总字符={total_chars}, 中文比例={chinese_ratio:.2f}")

        if chinese_chars > 0 and chinese_ratio > 0.5:  # 中文字符占比超过50%
            if not chinese_name:  # 取第一个中文游戏名
                chinese_name = content
                print(f"    设为中文名: '{content}'")
        # 判断是否为英文名/日文名（包含英文字母或日文假名，长度大于2）
        elif (re.search(r'[a-zA-Z\u3040-\u309f\u30a0-\u30ff]', content) and
              len(content) > 2):
            if not english_name:  # 取第一个合适的英文名/日文名
                english_name = content
                print(f"    设为英文名: '{content}'")

    # 如果没有找到合适的英文名，查找较短的英文标识
    if not english_name:
        print("未找到英文名，查找较短的英文标识...")
        for content in potential_names:
            # 查找包含英文字母或日文的内容（包括短标识）
            if (re.search(r'[a-zA-Z\u3040-\u309f\u30a0-\u30ff]', content) and
                content != chinese_name):
                english_name = content
                print(f"    找到英文标识: '{content}'")
                break

    print(f"最终结果: 中文名='{chinese_name}', 英文名='{english_name}'")
    return chinese_name, english_name

def debug_excel_file(excel_file):
    """调试Excel文件的游戏名提取"""
    print(f"📖 调试Excel文件: {excel_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"✅ 成功读取 {len(df)} 行数据")
        
        # 显示原始列名
        print(f"📋 原始列名: {list(df.columns)}")
        
        # 查找文件名列
        filename_col = None
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['文件', 'file', '名称', 'name']):
                filename_col = col
                break
        
        if not filename_col:
            filename_col = df.columns[0]  # 默认第一列
        
        print(f"🎯 使用文件名列: {filename_col}")
        
        # 显示前几行的文件名内容
        print("\n📋 前5行文件名内容:")
        for i in range(min(5, len(df))):
            filename = str(df[filename_col].iloc[i])
            print(f"\n--- 第 {i+1} 行 ---")
            chinese, english = extract_game_names(filename)
            print(f"提取结果: 中文='{chinese}', 英文='{english}'")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        debug_excel_file(sys.argv[1])
    else:
        print("用法: python debug_game_names.py <excel文件路径>")
        print("或者手动测试文件名:")
        
        # 手动测试一些示例
        test_filenames = [
            "[20250627][中文][摇鼠灵][Ratshaker][01008DB0233C8000]",
            "[][白虎：四神部队炎恋记][BYAKKO ～四神部隊炎恋記～][0100C30020F70000]",
            "[中文][超级马里奥][Super Mario][0100000000010000]"
        ]
        
        for filename in test_filenames:
            print(f"\n{'='*50}")
            extract_game_names(filename)
