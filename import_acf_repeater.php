<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once('wp-load.php');

// CSV 文件路径
$csvFile = __DIR__ . '/ripro_import.csv';

// 打开 CSV 文件
if (($handle = fopen($csvFile, 'r')) !== FALSE) {
    $header = fgetcsv($handle); // 读取表头
    while (($data = fgetcsv($handle)) !== FALSE) {
        $row = array_combine($header, $data);

        // 构建文章标题
        $post_title = $row['cao_downurl_new_0_name'] . '-' . $row['游戏英文名'];
        $post_id = post_exists($post_title);

        // 如果文章不存在，则跳过
        if (!$post_id) {
            echo "跳过：未找到文章标题为 {$post_title} 的文章<br>";
            continue;
        }

        // 插入视频内容到正文第一行（原样插入，无<pre>标签）
        if (!empty($row['视频'])) {
            $content = get_post_field('post_content', $post_id);
            $video_code = $row['视频'];
            $new_content = $video_code . "\n" . $content;
            wp_update_post([
                'ID' => $post_id,
                'post_content' => $new_content
            ]);
            echo "已插入视频到文章ID: {$post_id}<br>";
        }

        // 构建 Repeater 字段数组
        $cao_downurl_new = [];
        for ($i = 0; $i < 10; $i++) {
            $name = $row["cao_downurl_new_{$i}_name"] ?? '';
            $url  = $row["cao_downurl_new_{$i}_url"] ?? '';
            $pwd  = $row["cao_downurl_new_{$i}_pwd"] ?? '';
            if ($name && $url) {
                $cao_downurl_new[] = [
                    'name' => $name,
                    'url'  => $url,
                    'pwd'  => $pwd,
                ];
            }
        }

        // 写入 ACF Repeater 字段
        if (!empty($cao_downurl_new)) {
            update_post_meta($post_id, 'cao_downurl_new', $cao_downurl_new);
            echo "导入成功：文章ID {$post_id}，下载链接数：" . count($cao_downurl_new) . "<br>";
        } else {
            echo "跳过：文章ID {$post_id}，无下载链接<br>";
        }
    }
    fclose($handle);
} else {
    echo "无法打开CSV文件！";
}

// 工具函数：根据标题精确查找文章ID
function post_exists($title) {
    global $wpdb;
    $post_id = $wpdb->get_var($wpdb->prepare(
        "SELECT ID FROM $wpdb->posts WHERE post_title = %s AND post_status != 'trash' LIMIT 1",
        $title
    ));
    return $post_id ? intval($post_id) : 0;
}
?>