# 📊 Nintendo Switch Excel文本处理器

一个专门处理Nintendo Switch游戏Excel文本数据的工具，**专注于文本处理，不包含图片下载功能**。

## 📁 项目文件说明

### 核心程序文件
- **`excel_text_gui.py`** - 主程序（图形界面版本）
- **`excel_text_processor.py`** - 完整处理引擎（命令行版本）
- **`excel_text_processor_fast.py`** - 快速处理引擎（推荐）
- **`启动Excel文本处理器.command`** - 一键启动脚本（双击运行）

### 文档和示例
- **`Excel文本处理器使用说明.md`** - 详细的使用说明文档
- **`test_games.xlsx`** - 示例Excel文件
- **`README.md`** - 本文件

## 🚀 快速开始

### 最简单的启动方式
双击 `启动Excel文本处理器.command` 文件即可！

### 手动启动
```bash
python3 excel_text_gui.py
```

## 🎯 主要功能

1. **📊 Excel数据清理**
   - 自动清理和标准化游戏数据
   - 标准化列名格式
   - 添加必要的空列

2. **📝 游戏名称提取**
   - 从网页提取游戏名称
   - 优先提取英文名称
   - 备选日文名称

3. **📖 游戏介绍处理**
   - 从网页提取游戏介绍
   - 自动翻译成中文
   - 支持中英文对照

4. **🎨 图形界面**
   - 简洁的客户友好界面
   - 实时处理进度显示
   - 详细的操作日志

## 🚫 不包含的功能

- ❌ **图片下载** - 不下载游戏封面或截图
- ❌ **文件夹创建** - 不创建游戏文件夹结构
- ❌ **图片管理** - 专注于文本数据处理

## 📋 系统要求

- **Python 3.6+**
- **依赖包**：PyQt6, pandas, requests, beautifulsoup4, translators
- **网络连接**：需要访问 nswdl.com

## 🔧 依赖安装

程序会自动检查并安装依赖，也可以手动安装：

```bash
pip3 install PyQt6 pandas requests beautifulsoup4 translators
```

## 📖 使用流程

1. **选择Excel文件** - 包含游戏title id的Excel文件
2. **调整设置** - 处理数量、功能选项等
3. **选择处理模式**：
   - 🧹 **仅清理Excel格式** - 只清理数据格式
   - 🚀 **快速文本处理** - 清理 + 提取游戏名称（推荐）
   - 📊 **完整文本处理** - 清理 + 提取名称和介绍 + 翻译
4. **查看结果** - 处理完成后查看生成的Excel文件

## 📁 输入输出

### 输入要求
Excel文件必须包含 `title id` 列，格式如：
```
| title id         |
|------------------|
| 0100d680194b2000 |
| 0100a3d008c5c000 |
```

### 输出结果
处理后的Excel文件包含：
- `title id` - 原始游戏ID
- `游戏名称` - 提取的游戏名称
- `游戏介绍` - 英文游戏介绍
- `游戏介绍_中文` - 中文翻译

## 💡 使用示例

### 图形界面
1. 双击启动脚本
2. 选择Excel文件
3. 点击"完整文本处理"

### 命令行
```bash
# 完整处理
python3 excel_text_processor.py --excel test_games.xlsx

# 仅清理格式
python3 excel_text_processor.py --excel test_games.xlsx --clean-only

# 限制处理数量
python3 excel_text_processor.py --excel test_games.xlsx --max-games 5
```

## ⚠️ 注意事项

- 确保网络连接稳定
- 处理大量游戏时请耐心等待
- 建议备份原始Excel文件
- 首次运行可能需要安装依赖包

## 🆕 版本特性

### v1.0 (当前版本)
- ✅ 专注于Excel文本处理
- ✅ 移除图片下载功能
- ✅ 简化的图形界面
- ✅ 百度翻译支持（解决国内访问问题）
- ✅ 优化的处理流程

### 核心改进
- 🎯 专注文本处理，去除图片功能
- 🎨 简洁的用户界面
- 📊 详细的处理反馈
- 🔄 自动格式清理
- 🌐 稳定的网页内容提取

## 📞 技术支持

如遇问题，请查看：
1. 程序内的日志信息
2. `Excel文本处理器使用说明.md` 详细文档
3. 确认网络连接和依赖安装

---

**开发者**：Augment Agent
**版本**：v1.0 (文本专用版)
**更新时间**：2025年7月1日
