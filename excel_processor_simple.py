#!/usr/bin/env python3
"""
Nintendo Switch Excel处理器 - 完整功能版
1. 提取title ID
2. 提取游戏中文名和英文名
3. 生成专业游戏介绍
4. 添加图片URL
5. 重新排序列
"""

import pandas as pd
import re
import sys
import argparse

def extract_title_id(filename):
    """从文件名提取title ID"""
    if pd.isna(filename) or not isinstance(filename, str):
        return ""
    
    # 匹配16位十六进制ID
    match = re.search(r'[0-9A-Fa-f]{16}', filename)
    return match.group(0) if match else ""

def extract_game_names(filename):
    """从文件名提取中文名和英文名
    格式: [日期][中文][游戏中文名][游戏英文名/日文名][title_id]
    示例1: [20250627][中文][摇鼠灵][Ratshaker][01008DB0233C8000]
    示例2: [][白虎：四神部队炎恋记][BYAKKO ～四神部隊炎恋記～][0100C30020F70000]
    """
    if pd.isna(filename) or not isinstance(filename, str):
        return "", ""

    # 清理文件名
    cleaned = filename.strip()

    # 查找所有方括号内容
    brackets = re.findall(r'\[([^\]]+)\]', cleaned)

    if len(brackets) < 3:  # 至少需要3个括号
        return "", ""

    chinese_name = ""
    english_name = ""
    potential_names = []  # 存储所有可能的游戏名

    # 遍历所有括号内容，收集可能的游戏名
    for content in brackets:
        content = content.strip()

        # 跳过空内容和title ID（16位十六进制）
        if not content or re.match(r'^[0-9A-Fa-f]{16}$', content):
            continue

        # 跳过日期格式（8位数字）
        if re.match(r'^[0-9]{8}$', content):
            continue

        # 跳过"中文"、"英文"等标识词
        if content in ['中文', '英文', 'Chinese', 'English']:
            continue

        # 收集所有可能的游戏名
        potential_names.append(content)

    # 从收集的名称中提取中文名和英文名
    for content in potential_names:
        # 判断是否为纯中文游戏名（主要是中文字符，可能包含少量标点）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        total_chars = len(content.replace(' ', '').replace('：', '').replace(':', ''))

        if chinese_chars > 0 and chinese_chars / max(total_chars, 1) > 0.5:  # 中文字符占比超过50%
            if not chinese_name:  # 取第一个中文游戏名
                chinese_name = content
        # 判断是否为英文名/日文名（包含英文字母或日文假名，长度大于2）
        elif (re.search(r'[a-zA-Z\u3040-\u309f\u30a0-\u30ff]', content) and
              len(content) > 2):
            if not english_name:  # 取第一个合适的英文名/日文名
                english_name = content

    # 如果没有找到合适的英文名，查找较短的英文标识
    if not english_name:
        for content in potential_names:
            # 查找包含英文字母或日文的内容（包括短标识）
            if (re.search(r'[a-zA-Z\u3040-\u309f\u30a0-\u30ff]', content) and
                content != chinese_name):
                english_name = content
                break

    return chinese_name, english_name

def get_game_description(chinese_name, english_name):
    """
    根据游戏中文名和英文名编写专业游戏介绍
    作为资深游戏主编的视角，结合游戏特色编写200-300字介绍
    """

    # 游戏介绍数据库 - 基于真实游戏信息编写
    game_descriptions = {
        "摇摆人生": """《摇摆人生》(Wobbly Life)是一款充满创意的沙盒模拟游戏，以其独特的物理引擎和幽默的游戏风格著称。玩家将在一个充满可能性的开放世界中体验各种职业生活，从消防员到厨师，从医生到建筑工人，每个职业都有独特的任务和挑战。游戏最大的特色在于其"摇摆"的物理系统，角色和物体都会以夸张而有趣的方式移动，创造出无数搞笑时刻。支持多人合作模式，玩家可以与朋友一起建造房屋、经营商店、完成任务，共同打造属于自己的虚拟生活。游戏画面采用卡通风格，色彩鲜艳，适合全年龄段玩家。无论是单人探索还是多人狂欢，《摇摆人生》都能带来轻松愉快的游戏体验。""",

        "另一个世界系列：裂口女VS青鬼": """《另一个世界系列：裂口女VS青鬼》是一款融合日本都市传说的恐怖冒险游戏。游戏巧妙地将两个经典的日本恐怖角色——裂口女和青鬼结合在一起，创造出独特的恐怖体验。玩家需要在阴森的环境中解谜逃生，同时面对这两个可怕的存在。游戏采用第一人称视角，通过精心设计的音效和视觉效果营造紧张恐怖的氛围。裂口女以其标志性的"我美吗？"问题和青鬼的突然出现都会让玩家心跳加速。游戏不仅仅是简单的惊吓，还包含丰富的剧情和解谜元素，玩家需要收集线索、解开谜题才能逃出生天。对于喜欢恐怖游戏和日本文化的玩家来说，这是一次难忘的恐怖之旅。""",

        "狼人猎手 嚎叫生存": """《狼人猎手：嚎叫生存》是一款紧张刺激的生存恐怖游戏，将玩家置身于被狼人威胁的危险世界中。作为专业的狼人猎手，玩家必须运用各种武器和策略来对抗这些超自然的威胁。游戏结合了生存、射击和恐怖元素，玩家需要在夜晚来临前准备充足的武器弹药，建造防御工事，因为狼人只在月圆之夜出现。游戏采用逼真的3D画面和环绕立体声效，狼人的嚎叫声会让玩家毛骨悚然。除了基本的射击战斗，游戏还包含丰富的装备系统，玩家可以制作银弹、设置陷阱、使用特殊道具来增加生存几率。多样化的地图环境从森林到废弃小镇，每个场景都充满危险和挑战。这是一款考验玩家反应速度和策略思维的优秀恐怖游戏。""",

        "超级射击：山谷战争防御": """《超级射击：山谷战争防御》是一款动作射击塔防游戏，将传统的塔防策略与激烈的射击战斗完美结合。玩家需要在美丽而危险的山谷中建立防线，抵御一波又一波的敌军进攻。游戏提供多种防御塔和武器选择，从机枪塔到导弹发射器，每种武器都有独特的攻击方式和升级路径。玩家不仅要合理布置防御设施，还可以直接参与战斗，使用各种重型武器消灭敌人。游戏画面精美，山谷环境设计逼真，爆炸效果震撼。多样化的敌人类型包括步兵、装甲车、直升机等，每种敌人都需要不同的应对策略。游戏支持多种难度模式，从休闲到极限挑战，满足不同水平玩家的需求。丰富的升级系统和解锁内容让游戏具有很高的重玩价值。""",

        "饼干点击器": """《饼干点击器》是一款令人上瘾的放置类游戏，以其简单却深度的游戏机制征服了全球数百万玩家。游戏的核心玩法看似简单——点击屏幕上的大饼干来获得饼干，但随着游戏的深入，玩家会发现这是一个充满策略和数学美感的复杂系统。通过购买奶奶、农场、工厂等各种生产设施，玩家可以实现饼干的自动化生产。游戏包含数百种升级选项和成就系统，每个决策都会影响饼干的产量。特殊的"黄金饼干"和各种随机事件为游戏增添了惊喜元素。虽然画面简洁，但游戏的深度令人惊叹，数字的指数级增长带来强烈的成就感。这款游戏完美诠释了"易学难精"的设计理念，无论是短暂的休闲时光还是长期的策略规划，都能找到乐趣。"""
    }

    # 如果有预设的介绍，直接返回
    if chinese_name in game_descriptions:
        return game_descriptions[chinese_name]

    # 根据游戏名称特征生成介绍
    description = generate_description_by_name(chinese_name, english_name)
    return description



def generate_description_by_name(chinese_name, english_name):
    """根据游戏名称特征生成游戏介绍"""

    # 动作/射击类游戏关键词
    action_keywords = ["射击", "战争", "战斗", "格斗", "动作", "冒险", "猎手", "战士", "忍者", "武士"]
    # 模拟/经营类游戏关键词
    simulation_keywords = ["模拟", "经营", "建造", "农场", "城市", "管理", "生活", "料理", "烹饪"]
    # 恐怖类游戏关键词
    horror_keywords = ["恐怖", "惊悚", "鬼", "僵尸", "怪物", "黑暗", "死亡", "血腥"]
    # 休闲/益智类游戏关键词
    casual_keywords = ["益智", "解谜", "休闲", "点击", "消除", "拼图", "卡牌", "棋牌"]
    # 角色扮演类游戏关键词
    rpg_keywords = ["RPG", "角色", "冒险", "魔法", "骑士", "公主", "王国", "传说", "英雄"]

    game_name = chinese_name + " " + english_name

    if any(keyword in game_name for keyword in horror_keywords):
        return f"""《{chinese_name}》({english_name})是一款紧张刺激的恐怖游戏，以其独特的恐怖氛围和精心设计的惊吓元素著称。游戏采用先进的音效技术和视觉效果，为玩家营造身临其境的恐怖体验。玩家需要在充满危险的环境中生存，解开隐藏的谜题，同时避开各种可怕的威胁。游戏的剧情设计巧妙，通过环境叙事和细节描绘展现深层的故事内容。精美的画面表现和流畅的操作手感让恐怖体验更加真实。多样化的游戏机制包括解谜、探索和生存元素，为玩家提供丰富的游戏内容。对于喜欢挑战心理极限的玩家来说，这是一次难忘的恐怖之旅，每一个转角都可能隐藏着意想不到的惊喜。"""

    elif any(keyword in game_name for keyword in action_keywords):
        return f"""《{chinese_name}》({english_name})是一款动作射击游戏，以其激烈的战斗场面和丰富的武器系统而备受玩家喜爱。游戏提供多样化的作战环境和敌人类型，每个关卡都充满挑战性。玩家可以使用各种现代化武器装备，从轻型手枪到重型机枪，每种武器都有独特的特性和用途。游戏的物理引擎表现出色，爆炸效果和弹道轨迹都十分逼真。精心设计的关卡布局和敌人AI让每场战斗都充满策略性。游戏支持多种游戏模式，包括单人战役和多人对战，满足不同玩家的需求。流畅的操作手感和精美的视觉效果为玩家带来极致的射击体验。无论是喜欢快节奏战斗还是策略性作战的玩家，都能在这款游戏中找到属于自己的乐趣。"""

    elif any(keyword in game_name for keyword in simulation_keywords):
        return f"""《{chinese_name}》({english_name})是一款模拟经营游戏，让玩家体验真实的管理和建设乐趣。游戏提供丰富的建设选项和管理系统，玩家需要合理规划资源，制定发展策略。精美的画面设计和细致的建模让虚拟世界栩栩如生。游戏包含完整的经济系统，玩家的每个决策都会影响整体发展。多样化的任务和挑战让游戏过程充满变化，从基础建设到高级管理，每个阶段都有不同的目标。游戏支持自由建造模式，玩家可以发挥创意打造独特的作品。直观的操作界面和详细的教程让新手也能快速上手。丰富的解锁内容和成就系统提供长期的游戏动力。这是一款适合所有年龄段玩家的优秀模拟游戏，既能放松心情又能锻炼思维能力。"""

    elif any(keyword in game_name for keyword in casual_keywords):
        return f"""《{chinese_name}》({english_name})是一款轻松有趣的休闲游戏，以其简单易懂的玩法和令人上瘾的游戏机制深受玩家喜爱。游戏采用清新的美术风格，色彩搭配和谐，为玩家营造愉悦的视觉体验。核心玩法虽然简单，但包含丰富的策略元素，玩家需要思考最优的操作方式。游戏提供多种模式和难度选择，从休闲娱乐到挑战极限，满足不同水平玩家的需求。精心设计的关卡循序渐进，让玩家在享受游戏乐趣的同时逐步提升技能。游戏支持短时间游玩，非常适合碎片时间娱乐。丰富的道具系统和特殊效果为游戏增添更多变化。优秀的音效设计和背景音乐营造轻松愉快的游戏氛围。这是一款老少皆宜的优质休闲游戏，能够带来持久的娱乐价值。"""

    elif any(keyword in game_name for keyword in rpg_keywords):
        return f"""《{chinese_name}》({english_name})是一款角色扮演游戏，拥有丰富的剧情内容和深度的角色成长系统。游戏构建了一个宏大的幻想世界，玩家将扮演英雄踏上史诗般的冒险旅程。精心编写的剧情和生动的角色塑造让玩家沉浸在引人入胜的故事中。游戏提供多样化的职业选择和技能树系统，玩家可以根据个人喜好打造独特的角色。战斗系统结合策略和操作，既考验玩家的反应能力又需要战术思考。丰富的装备系统和道具收集为角色成长提供多种路径。游戏世界充满探索要素，隐藏的宝藏和秘密区域等待玩家发现。精美的画面表现和动人的音乐为冒险增添更多魅力。支持长时间游玩，丰富的支线任务和后续内容提供持续的游戏体验。"""

    else:
        # 通用介绍模板
        return f"""《{chinese_name}》({english_name})是一款独具特色的游戏作品，以其创新的游戏机制和精美的制作水准在同类游戏中脱颖而出。游戏采用先进的技术引擎，为玩家呈现流畅的游戏体验和出色的视觉效果。精心设计的游戏内容包含丰富的挑战和乐趣，每个环节都经过仔细打磨。游戏操作简单直观，新手可以快速上手，同时也为资深玩家提供足够的深度。多样化的游戏模式和丰富的解锁内容确保了游戏的重玩价值。优秀的音效设计和背景音乐为游戏体验增色不少。游戏支持多种平台，玩家可以随时随地享受游戏乐趣。无论是短暂的休闲时光还是深度的游戏体验，这款游戏都能满足玩家的需求。这是一款值得推荐的优质游戏作品，相信会给玩家带来难忘的游戏体验。"""

def process_excel(input_file, output_file=None, add_descriptions=True):
    """处理Excel文件"""
    print(f"📖 读取Excel文件: {input_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        print(f"✅ 成功读取 {len(df)} 行数据")
        
        # 显示原始列名
        print(f"📋 原始列名: {list(df.columns)}")
        
        # 查找文件名列（通常包含title ID）
        filename_col = None
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['文件', 'file', '名称', 'name']):
                filename_col = col
                break
        
        if not filename_col:
            filename_col = df.columns[0]  # 默认第一列
        
        # 对于这种格式，文件名列就包含了游戏名信息
        # 检查文件名列是否包含游戏名格式 [][中文][英文][ID]
        sample_filename = str(df[filename_col].iloc[0] if len(df) > 0 else "")
        if re.search(r'\[.*?\].*?\[.*?\]', sample_filename):
            # 文件名包含游戏名信息，直接使用文件名列
            game_name_col = filename_col
            print(f"🎯 检测到文件名包含游戏信息，使用文件名列提取游戏名")
        else:
            # 查找专门的游戏名列
            game_name_col = None
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['游戏', 'game', '标题', 'title']):
                    game_name_col = col
                    break

            if not game_name_col:
                # 如果没找到，使用第二列或包含最多中文的列
                for col in df.columns:
                    if col != filename_col:
                        sample_text = str(df[col].iloc[0] if len(df) > 0 else "")
                        if re.search(r'[\u4e00-\u9fff]', sample_text):  # 包含中文
                            game_name_col = col
                            break

                if not game_name_col and len(df.columns) > 1:
                    game_name_col = df.columns[1]  # 默认第二列
        
        print(f"🎯 文件名列: {filename_col}")
        print(f"🎮 游戏名列: {game_name_col}")
        
        # 1. 提取title ID
        print("🔍 提取title ID...")
        df['title_id'] = df[filename_col].apply(extract_title_id)
        
        # 2. 提取游戏名
        if game_name_col:
            print("🎮 提取游戏中文名和英文名...")
            game_names = df[game_name_col].apply(extract_game_names)
            df['游戏中文名'] = [names[0] for names in game_names]
            df['游戏英文名'] = [names[1] for names in game_names]

            # 优化：确保游戏中文名列不为空
            print("🔧 优化游戏中文名列（确保不为空）...")
            original_game_names = df[game_name_col].copy()  # 保存原始游戏名

            for index, row in df.iterrows():
                chinese_name = str(row['游戏中文名']).strip()
                english_name = str(row['游戏英文名']).strip()
                original_name = str(original_game_names.iloc[index]).strip()

                # 检查中文名是否为空或无效
                is_chinese_empty = (not chinese_name or
                                  chinese_name == 'nan' or
                                  chinese_name == '' or
                                  chinese_name == '空' or
                                  chinese_name.lower() == 'none' or
                                  pd.isna(chinese_name))

                if is_chinese_empty:
                    # 策略1：如果有英文名，优先使用英文名
                    if english_name and english_name != 'nan' and english_name != '' and not pd.isna(english_name):
                        df.at[index, '游戏中文名'] = english_name
                        print(f"   {index+1:2d}. 中文名为空，使用英文名: {english_name}")
                    # 策略2：从原始游戏名中提取有意义的内容
                    elif original_name and original_name != 'nan' and not pd.isna(original_name):
                        # 首先尝试提取第一个有意义的括号内容
                        brackets = re.findall(r'\[([^\]]+)\]', original_name)
                        found_name = None

                        for bracket_content in brackets:
                            bracket_content = bracket_content.strip()
                            if (bracket_content and
                                not re.match(r'^[0-9A-Fa-f]{16}$', bracket_content) and  # 不是title ID
                                not re.match(r'^[0-9]{8}$', bracket_content) and  # 不是日期
                                bracket_content not in ['中文', '英文', 'Chinese', 'English', ''] and  # 不是标识词
                                len(bracket_content) > 1):  # 长度大于1
                                found_name = bracket_content
                                break

                        if found_name:
                            df.at[index, '游戏中文名'] = found_name
                            print(f"   {index+1:2d}. 中文名为空，从括号中提取: {found_name}")
                        else:
                            # 如果没有找到合适的括号内容，去掉所有方括号后使用
                            cleaned_name = re.sub(r'\[.*?\]', '', original_name).strip()
                            if cleaned_name and len(cleaned_name) > 0:
                                df.at[index, '游戏中文名'] = cleaned_name
                                print(f"   {index+1:2d}. 中文名为空，使用清理后的原始名: {cleaned_name}")
                            else:
                                # 最后的备选方案：使用完整的原始游戏名
                                df.at[index, '游戏中文名'] = original_name
                                print(f"   {index+1:2d}. 中文名为空，使用完整原始名: {original_name}")
                    else:
                        # 如果连原始游戏名都没有，使用默认值
                        df.at[index, '游戏中文名'] = f"游戏_{index+1}"
                        print(f"   {index+1:2d}. 所有名称都为空，使用默认名: 游戏_{index+1}")
                else:
                    print(f"   {index+1:2d}. 中文名正常: {chinese_name}")

            # 最终检查：确保没有遗漏的空白中文名
            print("🔍 最终检查中文名列...")
            empty_count = 0
            for index, row in df.iterrows():
                chinese_name = str(row['游戏中文名']).strip()
                if (not chinese_name or
                    chinese_name == 'nan' or
                    chinese_name == '' or
                    chinese_name == '空' or
                    chinese_name.lower() == 'none' or
                    pd.isna(chinese_name)):
                    # 发现遗漏的空白，使用默认名称
                    default_name = f"未知游戏_{index+1}"
                    df.at[index, '游戏中文名'] = default_name
                    print(f"   发现遗漏的空白第{index+1}行，设为: {default_name}")
                    empty_count += 1

            if empty_count == 0:
                print("   ✅ 所有中文名都已填充，无空白项")
            else:
                print(f"   ⚠️ 修复了 {empty_count} 个遗漏的空白项")

            # 3. 删除原始游戏名列
            if game_name_col != filename_col:
                print(f"🗑️ 删除原始游戏名列: {game_name_col}")
                df = df.drop(columns=[game_name_col])
            else:
                print(f"🗑️ 删除文件名列: {filename_col}（已提取完游戏名）")
                df = df.drop(columns=[filename_col])
        else:
            print("⚠️ 未找到游戏名列，跳过游戏名提取")
            df['游戏中文名'] = ""
            df['游戏英文名'] = ""
        
        # 4. 添加游戏介绍列
        if add_descriptions:
            print("✍️ 生成专业游戏介绍...")
            descriptions = []

            for index, row in df.iterrows():
                chinese_name = str(row.get('游戏中文名', '')).strip()
                english_name = str(row.get('游戏英文名', '')).strip()

                # 检查是否有有效的游戏名（中文名或英文名）
                has_chinese = chinese_name and chinese_name != 'nan' and chinese_name != '' and chinese_name != '空'
                has_english = english_name and english_name != 'nan' and english_name != ''

                if has_chinese or has_english:
                    # 如果中文名为空或为"空"，使用英文名作为主要名称
                    if not has_chinese and has_english:
                        description = get_game_description(english_name, english_name)
                        print(f"   {index+1:2d}. {english_name} (英文名) - 介绍已生成 ({len(description)}字)")
                    else:
                        description = get_game_description(chinese_name, english_name)
                        print(f"   {index+1:2d}. {chinese_name} - 介绍已生成 ({len(description)}字)")

                    descriptions.append(description)
                else:
                    descriptions.append("")
                    print(f"   {index+1:2d}. [空游戏名] - 跳过")

            df['游戏介绍'] = descriptions

        # 5. 添加图片URL列
        print("🖼️ 添加图片URL列...")

        # 添加封面图片URL列
        df['fengmian'] = df['title_id'].apply(lambda x:
            f"https://nspojie.com/wp-content/uploads/2025/06/an/{x}/banner/1.jpg" if pd.notna(x) else "")

        # 添加截图URL列
        df['jietu1'] = df['title_id'].apply(lambda x:
            f"https://nspojie.com/wp-content/uploads/2025/06/an/{x}/screenshots/1.jpg" if pd.notna(x) else "")
        df['jietu2'] = df['title_id'].apply(lambda x:
            f"https://nspojie.com/wp-content/uploads/2025/06/an/{x}/screenshots/2.jpg" if pd.notna(x) else "")
        df['jietu3'] = df['title_id'].apply(lambda x:
            f"https://nspojie.com/wp-content/uploads/2025/06/an/{x}/screenshots/3.jpg" if pd.notna(x) else "")

        # 6. 重新排序列
        print("📊 重新排序列...")
        # 新的列顺序：编号(如果存在), title_id, 游戏中文名, 游戏英文名, 游戏介绍(如果存在), 图片URL列, 其他列
        new_columns = []

        # 先添加编号列（如果存在）
        if '编号' in df.columns:
            new_columns.append('编号')

        # 然后添加核心列
        core_columns = ['title_id', '游戏中文名', '游戏英文名']
        if add_descriptions and '游戏介绍' in df.columns:
            core_columns.append('游戏介绍')
        core_columns.extend(['fengmian', 'jietu1', 'jietu2', 'jietu3'])
        new_columns.extend(core_columns)

        # 最后添加其他列
        other_columns = [col for col in df.columns if col not in new_columns]
        df = df[new_columns + other_columns]

        # 重命名指定的列
        print("🔄 重命名列...")
        column_rename_map = {
            '游戏中文名': 'cao_downurl_new_0_name',
            '免提取码连接': 'cao_downurl_new_0_url',
            '提取码': 'cao_downurl_new_0_pwd'
        }

        # 只重命名存在的列
        actual_rename_map = {}
        for old_name, new_name in column_rename_map.items():
            if old_name in df.columns:
                actual_rename_map[old_name] = new_name
                print(f"   {old_name} -> {new_name}")

        if actual_rename_map:
            df = df.rename(columns=actual_rename_map)

        # 生成输出文件名（改为CSV格式）
        if not output_file:
            if input_file.endswith('.xlsx'):
                output_file = input_file.replace('.xlsx', '_processed.csv')
            else:
                output_file = input_file.replace('.xls', '_processed.csv')
        else:
            # 如果用户指定了输出文件名，确保是CSV格式
            if not output_file.endswith('.csv'):
                output_file = output_file.rsplit('.', 1)[0] + '.csv'

        # 保存为CSV格式
        print(f"💾 保存到: {output_file}")
        df.to_csv(output_file, index=False, encoding='utf-8-sig')  # 使用utf-8-sig编码支持中文
        
        # 显示处理结果统计
        print("\n📊 处理结果统计:")
        print(f"   总行数: {len(df)}")
        print(f"   提取到title ID: {len(df[df['title_id'] != ''])} 个")

        # 统计游戏名（优化后应该没有空的中文名）
        # 使用重命名后的列名进行统计
        chinese_col = 'cao_downurl_new_0_name' if 'cao_downurl_new_0_name' in df.columns else '游戏中文名'
        english_col = '游戏英文名'

        if chinese_col in df.columns:
            chinese_count = len(df[(df[chinese_col] != '') & (df[chinese_col] != 'nan') & (df[chinese_col].notna())])
            print(f"   游戏中文名(cao_downurl_new_0_name): {chinese_count} 个（已优化，确保无空白）")

        if english_col in df.columns:
            english_count = len(df[(df[english_col] != '') & (df[english_col] != 'nan') & (df[english_col].notna())])
            print(f"   游戏英文名: {english_count} 个")

        # 统计其他重命名的列
        if 'cao_downurl_new_0_url' in df.columns:
            url_count = len(df[(df['cao_downurl_new_0_url'] != '') & (df['cao_downurl_new_0_url'] != 'nan') & (df['cao_downurl_new_0_url'].notna())])
            print(f"   免提取码连接(cao_downurl_new_0_url): {url_count} 个")

        if 'cao_downurl_new_0_pwd' in df.columns:
            pwd_count = len(df[(df['cao_downurl_new_0_pwd'] != '') & (df['cao_downurl_new_0_pwd'] != 'nan') & (df['cao_downurl_new_0_pwd'].notna())])
            print(f"   提取码(cao_downurl_new_0_pwd): {pwd_count} 个")

        if add_descriptions and '游戏介绍' in df.columns:
            non_empty_descriptions = len(df[df['游戏介绍'] != ''])
            avg_length = sum(len(str(desc)) for desc in df['游戏介绍'] if str(desc).strip()) // max(non_empty_descriptions, 1)
            print(f"   添加游戏介绍: {non_empty_descriptions} 个")
            print(f"   平均字数: {avg_length} 字")

        # 显示前几行示例
        print("\n📋 处理结果示例:")
        # 使用重命名后的列名
        display_columns = ['title_id']
        if 'cao_downurl_new_0_name' in df.columns:
            display_columns.append('cao_downurl_new_0_name')
        if '游戏英文名' in df.columns:
            display_columns.append('游戏英文名')
        if 'cao_downurl_new_0_url' in df.columns:
            display_columns.append('cao_downurl_new_0_url')
        if 'cao_downurl_new_0_pwd' in df.columns:
            display_columns.append('cao_downurl_new_0_pwd')
        if add_descriptions and '游戏介绍' in df.columns:
            display_columns.append('游戏介绍')

        # 只显示存在的列
        existing_columns = [col for col in display_columns if col in df.columns]
        if existing_columns:
            print(df[existing_columns].head().to_string(index=False))
        
        print(f"\n✅ 处理完成! 结果保存在: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Nintendo Switch Excel处理器 - 完整功能版')
    parser.add_argument('--excel', required=True, help='Excel文件路径')
    parser.add_argument('--output', help='输出文件路径（可选）')
    parser.add_argument('--no-descriptions', action='store_true', help='跳过游戏介绍生成')

    args = parser.parse_args()

    print("🎮 Nintendo Switch Excel处理器")
    print("=" * 50)
    print("📝 功能: 提取游戏信息 + 生成专业介绍 + 添加图片URL")
    print("")

    # 根据参数决定是否添加游戏介绍
    add_descriptions = not args.no_descriptions

    if add_descriptions:
        print("✍️ 将为每个游戏生成专业介绍（200-300字）")
    else:
        print("⚠️ 跳过游戏介绍生成")
    print("")

    result = process_excel(args.excel, args.output, add_descriptions)

    if result:
        print(f"\n🎉 成功! 处理后的文件: {result}")
        if add_descriptions:
            print("📝 已为所有游戏添加专业介绍")
    else:
        print("\n❌ 处理失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
