# Nintendo Switch Excel处理器

完整功能的Excel游戏数据处理工具，一站式解决所有需求。

## 🎯 核心功能

✅ **提取title ID** - 从文件名自动提取16位十六进制ID
✅ **提取游戏名** - 分离中文名和英文名到独立列
✅ **生成游戏介绍** - 作为资深游戏主编，为每个游戏编写200-300字专业介绍
✅ **添加图片URL** - 自动生成封面和截图链接
✅ **重新排序** - 删除原列，按新格式排列
✅ **智能识别** - 自动检测Excel文件结构

## 📁 项目文件

```
标准化/
├── excel_processor_simple.py    # 核心处理程序
├── 处理Excel.command           # 一键启动脚本
└── 使用说明.md                 # 本文件
```

## 🚀 使用方法

### 方法1: 一键启动（推荐）
1. 将Excel文件放在程序目录中
2. 双击 `处理Excel.command` 文件
3. 按提示选择文件
4. 等待处理完成

### 方法2: 命令行
```bash
# 完整处理（包含游戏介绍）
python3 excel_processor_simple.py --excel 你的文件.xlsx

# 跳过游戏介绍生成（快速处理）
python3 excel_processor_simple.py --excel 你的文件.xlsx --no-descriptions
```

## 📊 支持的Excel格式

程序自动识别以下格式：

**文件名格式**: `[][中文名][英文名][title_id]`  
**示例**: `[][中文][Wobbly Life][010039501F11C000]`

**输出格式**:
- `title_id`: 010039501F11C000
- `游戏中文名`: 中文
- `游戏英文名`: Wobbly Life
- `游戏介绍`: 200-300字专业游戏介绍
- `fengmian`: 封面图片URL
- `jietu1/2/3`: 游戏截图URL
- 其他原有列保持不变

## ✨ 处理示例

**输入Excel**:
```
编号 | 已分享连接 | 提取码 | 文件名
1    | 链接...    | abc   | [][摇摆人生][Wobbly Life][010039501F11C000]
```

**输出Excel**:
```
title_id         | 游戏中文名 | 游戏英文名    | 游戏介绍           | fengmian | 编号 | 已分享连接 | 提取码
010039501F11C000 | 摇摆人生   | Wobbly Life  | 《摇摆人生》是... | 图片URL  | 1    | 链接...    | abc
```

## 🎯 特点

- **🚀 快速**: 几秒钟处理完成
- **🎯 准确**: 智能识别中英文名
- **✍️ 专业**: 资深游戏主编视角的专业介绍
- **🔧 简单**: 一键操作，无需配置
- **📊 清晰**: 处理结果统计和预览
- **💾 安全**: 生成新文件，不覆盖原文件
- **🎮 完整**: 一站式解决所有游戏数据处理需求

## 📋 处理统计

程序会显示：
- 总处理行数
- 成功提取的title ID数量
- 成功提取的中文名数量
- 成功提取的英文名数量
- 生成的游戏介绍数量和平均字数
- 前几行处理结果预览

## 🔧 技术要求

- Python 3.6+
- pandas库（用于Excel处理）

## 🎮 游戏介绍功能

### 专业内容创作
- **资深主编视角**: 以游戏媒体专业标准编写
- **字数控制**: 每个介绍200-300字，符合媒体报道要求
- **类型识别**: 智能识别游戏类型（动作、恐怖、模拟、休闲、RPG等）
- **内容丰富**: 包含玩法介绍、特色分析、画面音效、适用人群等

### 预设精品介绍
程序内置了热门游戏的精心编写介绍：
- 摇摆人生 (Wobbly Life)
- 另一个世界系列：裂口女VS青鬼
- 狼人猎手：嚎叫生存
- 超级射击：山谷战争防御
- 饼干点击器 (Cookie Clicker)

### 智能生成系统
对于其他游戏，程序会根据游戏名称特征智能生成专业介绍，确保每个游戏都有高质量的内容描述。

### 空中文名处理
- **智能识别**: 当中文名为空或为"空"时，自动使用英文名生成介绍
- **无缝切换**: 程序会自动判断并选择最合适的游戏名称
- **完整覆盖**: 确保每个游戏都能获得专业介绍，无论中英文名情况如何

---

**功能完整**: 一个程序解决所有游戏数据处理需求！🎮
