#!/bin/bash

echo "🎮 Nintendo Switch Excel处理器"
echo "================================"
echo ""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 查找Excel文件
echo "🔍 查找Excel文件..."
EXCEL_FILES=(*.xlsx *.xls)
VALID_FILES=()

for file in "${EXCEL_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        VALID_FILES+=("$file")
    fi
done

if [ ${#VALID_FILES[@]} -eq 0 ]; then
    echo "❌ 未找到Excel文件"
    echo "请将Excel文件放在程序目录中"
    read -p "按回车键退出..."
    exit 1
fi

echo "📊 找到Excel文件:"
for i in "${!VALID_FILES[@]}"; do
    echo "   $((i+1)). ${VALID_FILES[i]}"
done
echo ""

# 选择文件
if [ ${#VALID_FILES[@]} -eq 1 ]; then
    SELECTED_FILE="${VALID_FILES[0]}"
    echo "✅ 自动选择: $SELECTED_FILE"
else
    while true; do
        read -p "请选择文件 (1-${#VALID_FILES[@]}): " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#VALID_FILES[@]} ]; then
            SELECTED_FILE="${VALID_FILES[$((choice-1))]}"
            echo "✅ 已选择: $SELECTED_FILE"
            break
        else
            echo "❌ 无效选择，请重新输入"
        fi
    done
fi

echo ""
echo "🚀 开始处理..."
echo ""

# 执行处理
python3 excel_processor_simple.py --excel "$SELECTED_FILE"

echo ""
read -p "按回车键退出..."
