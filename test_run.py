#!/usr/bin/env python3

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from excel_processor_simple import process_excel

def main():
    excel_file = "导出记录-2025年7月14日12时10分1秒.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return
    
    print(f"📖 开始处理文件: {excel_file}")
    result = process_excel(excel_file, None, True)
    
    if result:
        print(f"✅ 处理完成: {result}")
    else:
        print("❌ 处理失败")

if __name__ == "__main__":
    main()
